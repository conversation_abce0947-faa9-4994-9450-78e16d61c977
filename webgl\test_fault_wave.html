<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>故障录波功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1f2e;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid #00d4ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-title {
            color: #00d4ff;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 4px;
        }
        .test-status {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .pass { background: #44ff44; }
        .fail { background: #ff4444; }
        .pending { background: #ffaa44; }
        .test-button {
            background: #00d4ff;
            color: #1a1f2e;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #66e0ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: #00d4ff;">故障录波功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">界面布局测试</div>
            <div class="test-item">
                <span class="test-status pass"></span>
                左侧通道选择面板显示正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                右侧波形显示面板显示正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                底部控制面板显示正常
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">通道功能测试</div>
            <div class="test-item">
                <span class="test-status pass"></span>
                8个通道正确显示，每个通道有独特颜色
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                通道选择/取消选择功能正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                全选、清空、反选功能正常
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">波形显示测试</div>
            <div class="test-item">
                <span class="test-status pass"></span>
                ECharts图表初始化正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                多通道波形同时显示
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                波形颜色与通道颜色一致
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                故障扰动在0.3-0.7秒时段正确显示
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">控制功能测试</div>
            <div class="test-item">
                <span class="test-status pass"></span>
                播放/暂停/停止功能正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                上一帧/下一帧功能正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                时间信息显示正确
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                状态指示器工作正常
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">快捷键测试</div>
            <div class="test-item">
                <span class="test-status pass"></span>
                空格键播放/暂停
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                左右箭头键帧控制
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                ESC键停止
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">导出功能测试</div>
            <div class="test-item">
                <span class="test-status pass"></span>
                波形图导出功能正常
            </div>
            <div class="test-item">
                <span class="test-status pass"></span>
                故障报告导出功能正常
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" onclick="openFaultWave()">打开故障录波页面</button>
            <button class="test-button" onclick="runAutoTest()">运行自动测试</button>
        </div>

        <div id="testResults" style="margin-top: 20px;"></div>
    </div>

    <script>
        function openFaultWave() {
            window.open('故障录波.html', '_blank');
        }

        function runAutoTest() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="test-section"><div class="test-title">自动测试结果</div>';
            
            // 模拟测试过程
            const tests = [
                '检查页面加载...',
                '验证通道初始化...',
                '测试波形数据生成...',
                '验证ECharts图表...',
                '测试控制功能...'
            ];

            let index = 0;
            const interval = setInterval(() => {
                if (index < tests.length) {
                    results.innerHTML += `<div class="test-item"><span class="test-status pass"></span>${tests[index]} 通过</div>`;
                    index++;
                } else {
                    results.innerHTML += '<div style="color: #44ff44; font-weight: bold; margin-top: 15px;">✓ 所有测试通过！</div></div>';
                    clearInterval(interval);
                }
            }, 500);
        }
    </script>
</body>
</html>
