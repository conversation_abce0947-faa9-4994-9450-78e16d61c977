<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 参数曲线</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 参数曲线页面专用样式 */
        .parameter-curve-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .parameter-curve-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .parameter-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 10;
        }

        .parameter-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .parameter-title i {
            color: var(--accent-color);
            font-size: 32px;
        }

        /* 主内容区域 */
        .parameter-main-content {
            height: calc(100% - 80px);
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        /* 左侧控制面板 */
        .parameter-control-panel {
            width: 350px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        /* 时间筛选区域 */
        .time-filter-section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--accent-color);
        }

        .time-mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .time-mode-btn {
            flex: 1;
            padding: 8px 12px;
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            text-align: center;
        }

        .time-mode-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--accent-color);
        }

        .time-mode-btn.active {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .history-time-selector {
            display: none;
        }

        .history-time-selector.show {
            display: block;
        }

        .history-time-select {
            width: 100%;
            padding: 8px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 12px;
        }

        .history-time-select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        /* 曲线类型选择区域 */
        .curve-type-section {
            margin-bottom: 25px;
        }

        .curve-type-select {
            width: 100%;
            padding: 10px 12px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            color: var(--text-primary);
            font-size: 14px;
            cursor: pointer;
        }

        .curve-type-select:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .curve-type-select option {
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 8px;
        }

        /* 参数控制区域 */
        .parameter-control-section {
            margin-bottom: 25px;
        }

        .parameter-list {
            display: none;
            flex-direction: column;
            gap: 12px;
        }

        .parameter-list.show {
            display: flex;
        }

        .parameter-item {
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 12px;
        }

        .parameter-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .parameter-color-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .parameter-name {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .parameter-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .parameter-switch {
            width: 40px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .parameter-switch.active {
            background: var(--accent-color);
        }

        .parameter-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .parameter-switch.active::after {
            transform: translateX(20px);
        }

        /* 右侧图表区域 */
        .parameter-chart-area {
            flex: 1;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .chart-control-btn {
            padding: 8px 16px;
            background: rgba(0, 212, 255, 0.2);
            border: 1px solid var(--accent-color);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .chart-control-btn:hover {
            background: rgba(0, 212, 255, 0.3);
            transform: translateY(-2px);
        }

        .chart-control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .chart-control-btn.active {
            background: rgba(0, 212, 255, 0.4);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        /* 时间导航控制区域 */
        .time-navigation-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .nav-control-btn {
            padding: 6px 12px;
            background: rgba(42, 49, 66, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 6px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 11px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .nav-control-btn:hover {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--accent-color);
        }

        .nav-control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .nav-control-btn.active {
            background: rgba(0, 212, 255, 0.3);
            border-color: var(--accent-color);
        }

        .chart-container {
            flex: 1;
            min-height: 500px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        /* 滚动条样式 */
        .parameter-control-panel::-webkit-scrollbar {
            width: 6px;
        }

        .parameter-control-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .parameter-control-panel::-webkit-scrollbar-thumb {
            background: var(--accent-color);
            border-radius: 3px;
        }

        .parameter-control-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="parameter-curve-container">
        <!-- 顶部标题栏 -->
        <header class="parameter-header" style="display: none;">
            <h1 class="parameter-title">
                <i class="fas fa-chart-area"></i>
                参数曲线监控
            </h1>
        </header>

        <!-- 主内容区域 -->
        <main class="parameter-main-content">
            <!-- 左侧控制面板 -->
            <aside class="parameter-control-panel">
                <!-- 时间筛选 -->
                <div class="time-filter-section">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        时间筛选
                    </h3>
                    <div class="time-mode-selector">
                        <button class="time-mode-btn active" id="realtimeMode" onclick="switchTimeMode('realtime')">
                            实时模式
                        </button>
                        <button class="time-mode-btn" id="historyMode" onclick="switchTimeMode('history')">
                            历史模式
                        </button>
                    </div>
                    <div class="history-time-selector" id="historyTimeSelector">
                        <select class="history-time-select" id="historyTimeSelect" onchange="updateHistoryTime()">
                            <option value="">请选择历史时间点</option>
                            <option value="2025-07-07-14:30:00">2025-07-07 14:30:00</option>
                            <option value="2025-07-07-13:15:00">2025-07-07 13:15:00</option>
                            <option value="2025-07-07-12:00:00">2025-07-07 12:00:00</option>
                            <option value="2025-07-07-10:45:00">2025-07-07 10:45:00</option>
                            <option value="2025-07-07-09:30:00">2025-07-07 09:30:00</option>
                            <option value="2025-07-06-16:20:00">2025-07-06 16:20:00</option>
                            <option value="2025-07-06-14:10:00">2025-07-06 14:10:00</option>
                            <option value="2025-07-06-11:55:00">2025-07-06 11:55:00</option>
                        </select>
                    </div>
                </div>

                <!-- 曲线类型选择 -->
                <div class="curve-type-section">
                    <h3 class="section-title">
                        <i class="fas fa-list"></i>
                        曲线类型选择
                    </h3>
                    <select class="curve-type-select" id="curveTypeSelect" onchange="selectCurveType()">
                        <option value="">请选择曲线类型</option>
                        <!-- 曲线类型选项将通过JavaScript动态生成 -->
                    </select>
                </div>

                <!-- 参数控制 -->
                <div class="parameter-control-section">
                    <h3 class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        参数控制
                        <span class="parameter-count" id="parameterCount" style="font-size: 12px; color: var(--text-secondary); margin-left: 10px;">
                            (未选择曲线类型)
                        </span>
                    </h3>
                    <div class="parameter-list" id="parameterList">
                        <!-- 参数控制项将通过JavaScript动态生成 -->
                    </div>
                    <div class="parameter-help" id="parameterHelp" style="
                        padding: 15px;
                        background: rgba(0, 212, 255, 0.1);
                        border: 1px solid rgba(0, 212, 255, 0.3);
                        border-radius: 8px;
                        margin-top: 15px;
                        font-size: 12px;
                        color: var(--text-secondary);
                        line-height: 1.4;
                    ">
                        <i class="fas fa-info-circle" style="color: var(--accent-color); margin-right: 5px;"></i>
                        请先选择一个曲线类型，然后使用开关控制参数的状态值。图表将同时显示所有12个参数的状态（0=关闭，1=开启）。使用时间导航按钮可以查看不同时间点的数据。
                    </div>
                </div>
            </aside>

            <!-- 右侧图表区域 -->
            <section class="parameter-chart-area">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        参数状态监控
                    </h3>
                    <div class="chart-controls">
                        <button class="chart-control-btn" onclick="resetChart()">
                            <i class="fas fa-redo"></i> 重置
                        </button>
                        <button class="chart-control-btn" onclick="exportChart()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>

                <!-- 时间导航控制 -->
                <div class="time-navigation-controls">
                    <button class="nav-control-btn" id="goToFirstBtn" onclick="goToFirst()" title="回到最初">
                        <i class="fas fa-fast-backward"></i>
                        <span>最初</span>
                    </button>
                    <button class="nav-control-btn" id="stepBackwardBtn" onclick="stepBackward()" title="后退一个间隔">
                        <i class="fas fa-step-backward"></i>
                        <span>后退</span>
                    </button>
                    <button class="nav-control-btn" id="pauseAutoScrollBtn" onclick="toggleAutoScroll()" title="暂停自动滚动">
                        <i class="fas fa-pause"></i>
                        <span>暂停滚动</span>
                    </button>
                    <button class="nav-control-btn" id="stepForwardBtn" onclick="stepForward()" title="前进一个间隔">
                        <i class="fas fa-step-forward"></i>
                        <span>前进</span>
                    </button>
                    <button class="nav-control-btn" id="goToLatestBtn" onclick="goToLatest()" title="显示最新">
                        <i class="fas fa-fast-forward"></i>
                        <span>最新</span>
                    </button>
                    <button class="nav-control-btn active" id="enableAutoScrollBtn" onclick="toggleAutoScroll()" title="开启自动滚动" style="display: none;">
                        <i class="fas fa-play"></i>
                        <span>开启滚动</span>
                    </button>
                </div>

                <div class="chart-container" id="parameterChart">
                    <!-- ECharts图表将在这里渲染 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 参数曲线页面脚本
         * 处理曲线类型选择、参数控制和图表显示
         */

        // 全局变量
        let parameterChart = null;
        let chartUpdateInterval = null;
        let chartData = {};
        let allParameterData = []; // 存储所有时间点的完整数据
        let currentTimeIndex = 0; // 当前显示的时间点索引
        let isAutoScrollEnabled = true; // 自动滚动状态
        let currentTimeMode = 'realtime'; // 'realtime' 或 'history'
        let selectedHistoryTime = null;
        let parameterStates = {}; // 存储每个参数的当前状态（0或1）

        // 曲线类型配置（现在都是开关型）
        const curveTypes = [
            { id: 'io-input-1', name: 'IO状态输入1' },
            { id: 'io-input-2', name: 'IO状态输入2' },
            { id: 'io-output-1', name: 'IO状态输出1' },
            { id: 'io-output-2', name: 'IO状态输出2' },
            { id: 'system-status', name: '系统状态' },
            { id: 'dsp-status-1', name: 'DSP状态1' },
            { id: 'dsp-status-2', name: 'DSP状态2' },
            { id: 'dsp-status-3', name: 'DSP状态3' },
            { id: 'dc-voltage', name: '直流电压' },
            { id: 'direct-current', name: '直接电流' },
            { id: 'indirect-current', name: '间接电流有功' }
        ];

        // 参数配置（当选择特定曲线类型后显示）
        const parameters = [
            { id: 'manual-reactive', name: '手动给定无功', color: '#ff6b6b' },
            { id: 'pll-mode', name: '锁相环模式', color: '#4ecdc4' },
            { id: 'control-mode', name: '控制模式选择', color: '#45b7d1' },
            { id: 'low-voltage-support', name: '低电压无功支撑', color: '#96ceb4' },
            { id: 'unit-level', name: '单元级数', color: '#feca57' },
            { id: 'ct-sampling', name: 'CT采样点位置', color: '#ff9ff3' },
            { id: 'phase-number', name: '相数', color: '#54a0ff' },
            { id: 'master-slave', name: '主从机模式', color: '#5f27cd' },
            { id: 'svg-connection', name: 'SVG连接方式', color: '#00d2d3' },
            { id: 'balance-control', name: '平衡控制使能', color: '#ff6348' },
            { id: 'voltage-feedforward', name: '电压前馈模式', color: '#2ed573' },
            { id: 'idle-mode', name: '空载模式使能', color: '#ffa502' }
        ];

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initParameterCurvePage();
        });

        /**
         * 初始化参数曲线页面
         */
        function initParameterCurvePage() {
            console.log('初始化参数曲线页面...');

            // 初始化曲线类型选择
            initCurveTypeSelection();

            // 初始化参数状态
            initParameterStates();

            // 初始化图表
            initParameterChart();

            // 开始数据更新
            startDataUpdate();

            // 更新导航按钮状态
            updateNavigationButtons();
        }

        /**
         * 初始化参数状态
         */
        function initParameterStates() {
            parameters.forEach(param => {
                parameterStates[param.id] = 0; // 默认所有参数都是关闭状态
            });
        }

        /**
         * 初始化曲线类型选择
         */
        function initCurveTypeSelection() {
            const select = document.getElementById('curveTypeSelect');
            if (!select) return;

            // 清空现有选项（保留默认选项）
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }

            // 添加曲线类型选项
            curveTypes.forEach(type => {
                const option = document.createElement('option');
                option.value = type.id;
                option.textContent = type.name;
                select.appendChild(option);
            });
        }

        /**
         * 选择曲线类型（下拉框选择）
         */
        function selectCurveType() {
            const select = document.getElementById('curveTypeSelect');
            if (!select) return;

            const selectedTypeId = select.value;

            if (selectedTypeId) {
                // 清空之前的图表数据
                resetChart();
                // 显示对应的参数控制
                showParameterControls(selectedTypeId);
            } else {
                // 未选择任何类型，隐藏参数控制
                hideParameterControls();
            }
        }

        /**
         * 切换时间模式
         * @param {string} mode - 时间模式 ('realtime' 或 'history')
         */
        function switchTimeMode(mode) {
            currentTimeMode = mode;

            // 更新按钮状态
            document.getElementById('realtimeMode').classList.toggle('active', mode === 'realtime');
            document.getElementById('historyMode').classList.toggle('active', mode === 'history');

            // 显示/隐藏历史时间选择器
            const historySelector = document.getElementById('historyTimeSelector');
            if (historySelector) {
                historySelector.classList.toggle('show', mode === 'history');
            }

            // 重置历史时间选择
            if (mode === 'realtime') {
                selectedHistoryTime = null;
                const historySelect = document.getElementById('historyTimeSelect');
                if (historySelect) {
                    historySelect.value = '';
                }
            }

            // 更新图表数据
            updateChartForTimeMode();
        }

        /**
         * 更新历史时间选择
         */
        function updateHistoryTime() {
            const select = document.getElementById('historyTimeSelect');
            if (!select) return;

            selectedHistoryTime = select.value;
            updateChartForTimeMode();
        }

        /**
         * 根据时间模式更新图表
         */
        function updateChartForTimeMode() {
            // 清空现有数据
            allParameterData = [];
            currentTimeIndex = 0;

            // 重新初始化图表显示
            if (Object.keys(parameterStates).length > 0) {
                initAllParametersChart();
            }

            console.log(`切换到${currentTimeMode}模式${selectedHistoryTime ? ', 时间点: ' + selectedHistoryTime : ''}`);
        }

        /**
         * 初始化所有参数的图表显示
         */
        function initAllParametersChart() {
            if (!parameterChart) return;

            // 创建所有参数的数据系列
            const seriesData = parameters.map(param => ({
                name: param.name,
                type: 'line',
                data: [],
                lineStyle: {
                    color: param.color,
                    width: 2
                },
                itemStyle: { color: param.color },
                smooth: false,
                symbol: 'circle',
                symbolSize: 4,
                step: 'end'
            }));

            const option = parameterChart.getOption();
            option.series = seriesData;
            parameterChart.setOption(option);

            console.log('初始化所有参数图表显示');
        }

        /**
         * 更新当前参数显示
         */
        function updateCurrentParameterDisplay() {
            if (!parameterChart) return;

            const now = new Date();

            // 创建当前时间点的数据
            const currentData = {
                time: now,
                values: { ...parameterStates }
            };

            // 添加到数据历史
            allParameterData.push(currentData);

            // 保持数据点数量在合理范围内
            if (allParameterData.length > 100) {
                allParameterData.shift();
            }

            // 更新当前时间索引
            currentTimeIndex = allParameterData.length - 1;

            // 更新图表
            updateChartDisplay();
        }

        /**
         * 显示参数控制
         * @param {string} typeId - 曲线类型ID
         */
        function showParameterControls(typeId) {
            const parameterList = document.getElementById('parameterList');
            const parameterCount = document.getElementById('parameterCount');

            if (!parameterList) return;

            const curveType = curveTypes.find(type => type.id === typeId);
            if (!curveType) return;

            parameterList.innerHTML = '';

            // 显示所有参数（现在都是开关型控制）
            parameters.forEach(param => {
                const item = document.createElement('div');
                item.className = 'parameter-item';

                // 根据当前参数状态设置开关状态
                const isActive = parameterStates[param.id] === 1;
                const switchClass = isActive ? 'parameter-switch active' : 'parameter-switch';

                item.innerHTML = `
                    <div class="parameter-header">
                        <div class="parameter-color-dot" style="background-color: ${param.color}"></div>
                        <span class="parameter-name">${param.name}</span>
                    </div>
                    <div class="parameter-controls">
                        <div class="${switchClass}" onclick="toggleParameterValue('${param.id}')"></div>
                    </div>
                `;

                parameterList.appendChild(item);
            });

            // 更新状态指示器
            if (parameterCount) {
                parameterCount.textContent = `(${curveType.name} - ${parameters.length}个参数)`;
            }

            // 隐藏帮助信息
            const parameterHelp = document.getElementById('parameterHelp');
            if (parameterHelp) {
                parameterHelp.style.display = 'none';
            }

            parameterList.classList.add('show');

            // 初始化图表显示所有参数
            initAllParametersChart();
        }

        /**
         * 隐藏参数控制
         */
        function hideParameterControls() {
            const parameterList = document.getElementById('parameterList');
            const parameterCount = document.getElementById('parameterCount');
            const parameterHelp = document.getElementById('parameterHelp');

            if (parameterList) {
                parameterList.classList.remove('show');
            }

            if (parameterCount) {
                parameterCount.textContent = '(未选择曲线类型)';
            }

            // 显示帮助信息
            if (parameterHelp) {
                parameterHelp.style.display = 'block';
            }
        }

        /**
         * 切换参数值（0/1状态）
         * @param {string} paramId - 参数ID
         */
        function toggleParameterValue(paramId) {
            const switchEl = event.target;

            // 切换参数状态
            parameterStates[paramId] = parameterStates[paramId] === 1 ? 0 : 1;

            // 更新开关显示状态
            switchEl.classList.toggle('active', parameterStates[paramId] === 1);

            // 立即更新图表显示当前参数状态
            updateCurrentParameterDisplay();

            console.log(`参数 ${paramId} 状态切换为: ${parameterStates[paramId]}`);
        }

        /**
         * 时间导航控制函数
         */

        /**
         * 回到最初时间点
         */
        function goToFirst() {
            if (allParameterData.length === 0) return;

            currentTimeIndex = 0;
            updateChartDisplay();
            updateNavigationButtons();
            console.log('跳转到最初时间点');
        }

        /**
         * 显示最新时间点
         */
        function goToLatest() {
            if (allParameterData.length === 0) return;

            currentTimeIndex = allParameterData.length - 1;
            updateChartDisplay();
            updateNavigationButtons();
            console.log('跳转到最新时间点');
        }

        /**
         * 前进一个时间间隔
         */
        function stepForward() {
            if (allParameterData.length === 0) return;

            if (currentTimeIndex < allParameterData.length - 1) {
                currentTimeIndex++;
                updateChartDisplay();
                updateNavigationButtons();
                console.log(`前进到时间索引: ${currentTimeIndex}`);
            }
        }

        /**
         * 后退一个时间间隔
         */
        function stepBackward() {
            if (allParameterData.length === 0) return;

            if (currentTimeIndex > 0) {
                currentTimeIndex--;
                updateChartDisplay();
                updateNavigationButtons();
                console.log(`后退到时间索引: ${currentTimeIndex}`);
            }
        }

        /**
         * 切换自动滚动状态
         */
        function toggleAutoScroll() {
            isAutoScrollEnabled = !isAutoScrollEnabled;

            const pauseBtn = document.getElementById('pauseAutoScrollBtn');
            const enableBtn = document.getElementById('enableAutoScrollBtn');

            if (isAutoScrollEnabled) {
                // 开启自动滚动
                pauseBtn.style.display = 'flex';
                enableBtn.style.display = 'none';
                pauseBtn.classList.remove('active');
                enableBtn.classList.add('active');

                // 自动跳转到最新数据
                goToLatest();
            } else {
                // 暂停自动滚动
                pauseBtn.style.display = 'none';
                enableBtn.style.display = 'flex';
                pauseBtn.classList.add('active');
                enableBtn.classList.remove('active');
            }

            updateNavigationButtons();
            console.log(`自动滚动状态: ${isAutoScrollEnabled ? '开启' : '暂停'}`);
        }

        /**
         * 更新导航按钮状态
         */
        function updateNavigationButtons() {
            const goToFirstBtn = document.getElementById('goToFirstBtn');
            const stepBackwardBtn = document.getElementById('stepBackwardBtn');
            const stepForwardBtn = document.getElementById('stepForwardBtn');
            const goToLatestBtn = document.getElementById('goToLatestBtn');

            if (!goToFirstBtn || !stepBackwardBtn || !stepForwardBtn || !goToLatestBtn) return;

            const hasData = allParameterData.length > 0;
            const isAtFirst = currentTimeIndex === 0;
            const isAtLatest = currentTimeIndex === allParameterData.length - 1;

            // 更新按钮可用状态
            goToFirstBtn.disabled = !hasData || isAtFirst;
            stepBackwardBtn.disabled = !hasData || isAtFirst;
            stepForwardBtn.disabled = !hasData || isAtLatest || isAutoScrollEnabled;
            goToLatestBtn.disabled = !hasData || isAtLatest;
        }

        /**
         * 更新图表显示
         */
        function updateChartDisplay() {
            if (!parameterChart || allParameterData.length === 0) return;

            // 根据当前时间索引和显示模式更新图表
            let displayData;

            if (isAutoScrollEnabled) {
                // 自动滚动模式：显示所有历史数据
                displayData = allParameterData;
            } else {
                // 手动导航模式：显示当前时间点及之前的数据
                displayData = allParameterData.slice(0, currentTimeIndex + 1);
            }

            // 为每个参数创建数据系列
            const seriesData = parameters.map(param => {
                const data = displayData.map(item => [
                    item.time,
                    item.values[param.id] || 0
                ]);

                return {
                    name: param.name,
                    type: 'line',
                    data: data,
                    lineStyle: {
                        color: param.color,
                        width: 2
                    },
                    itemStyle: { color: param.color },
                    smooth: false,
                    symbol: 'circle',
                    symbolSize: 4,
                    step: 'end',
                    animation: false
                };
            });

            const option = parameterChart.getOption();
            option.series = seriesData;
            parameterChart.setOption(option, false, true);
        }

        /**
         * 初始化参数图表
         */
        function initParameterChart() {
            const chartContainer = document.getElementById('parameterChart');
            if (!chartContainer) return;
            
            parameterChart = echarts.init(chartContainer);
            
            const option = {
                backgroundColor: 'transparent',
                grid: {
                    left: '5%',
                    right: '5%',
                    top: '10%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'time',
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        fontSize: 12
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.2)' }
                    }
                },
                yAxis: {
                    type: 'value',
                    min: -0.2,
                    max: 1.2,
                    interval: 0.5,
                    axisLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.5)' }
                    },
                    axisLabel: {
                        color: '#ffffff',
                        fontSize: 12,
                        formatter: function(value) {
                            if (value === 0) return '关闭 (0)';
                            if (value === 1) return '开启 (1)';
                            return value;
                        }
                    },
                    splitLine: {
                        lineStyle: { color: 'rgba(0, 212, 255, 0.2)' }
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: 'rgba(0, 212, 255, 0.5)',
                    textStyle: { color: '#ffffff' },
                    formatter: function(params) {
                        let result = params[0].axisValueLabel + '<br/>';
                        params.forEach(param => {
                            const status = param.value[1] === 1 ? '开启' : '关闭';
                            result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${status} (${param.value[1]})<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    textStyle: { color: '#ffffff' },
                    top: '5%'
                },
                series: []
            };
            
            parameterChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', () => {
                if (parameterChart) {
                    parameterChart.resize();
                }
            });
        }



        /**
         * 开始数据更新
         */
        function startDataUpdate() {
            chartUpdateInterval = setInterval(() => {
                // 只有在自动滚动开启时才生成新数据
                if (!isAutoScrollEnabled) return;

                const now = new Date();

                // 生成当前时间点所有参数的数据
                const currentValues = {};

                parameters.forEach((param, paramIndex) => {
                    let value;

                    if (currentTimeMode === 'realtime') {
                        // 实时模式：如果参数开关是开启的，使用用户设置的值，否则生成模拟数据
                        if (parameterStates[param.id] !== undefined) {
                            // 使用用户设置的参数值
                            value = parameterStates[param.id];
                        } else {
                            // 生成模拟数据
                            const timeOffset = now.getTime() / 1000;

                            switch(param.id) {
                                case 'manual-reactive':
                                    value = Math.sin(timeOffset / 3 + paramIndex) > 0 ? 1 : 0;
                                    break;
                                case 'pll-mode':
                                    value = Math.cos(timeOffset / 4 + paramIndex) > 0.3 ? 1 : 0;
                                    break;
                                case 'control-mode':
                                    value = Math.sin(timeOffset / 2.5 + paramIndex) > -0.2 ? 1 : 0;
                                    break;
                                case 'low-voltage-support':
                                    value = Math.cos(timeOffset / 5 + paramIndex) > 0.5 ? 1 : 0;
                                    break;
                                case 'unit-level':
                                    value = Math.sin(timeOffset / 3.5 + paramIndex) > 0.1 ? 1 : 0;
                                    break;
                                case 'ct-sampling':
                                    value = Math.cos(timeOffset / 2.8 + paramIndex) > -0.1 ? 1 : 0;
                                    break;
                                case 'phase-number':
                                    value = Math.sin(timeOffset / 4.2 + paramIndex) > 0.4 ? 1 : 0;
                                    break;
                                case 'master-slave':
                                    value = Math.cos(timeOffset / 3.8 + paramIndex) > 0.2 ? 1 : 0;
                                    break;
                                case 'svg-connection':
                                    value = Math.sin(timeOffset / 2.2 + paramIndex) > -0.3 ? 1 : 0;
                                    break;
                                case 'balance-control':
                                    value = Math.cos(timeOffset / 4.5 + paramIndex) > 0.6 ? 1 : 0;
                                    break;
                                case 'voltage-feedforward':
                                    value = Math.sin(timeOffset / 3.2 + paramIndex) > 0 ? 1 : 0;
                                    break;
                                case 'idle-mode':
                                    value = Math.cos(timeOffset / 5.5 + paramIndex) > 0.3 ? 1 : 0;
                                    break;
                                default:
                                    value = Math.random() > 0.5 ? 1 : 0;
                            }
                        }
                    } else {
                        // 历史模式：生成固定的历史数据模式
                        const historyPattern = getHistoryPattern(param.id, selectedHistoryTime);
                        const timeIndex = Math.floor((now.getTime() / 1000) % historyPattern.length);
                        value = historyPattern[timeIndex];
                    }

                    currentValues[param.id] = value;
                });

                // 添加到数据历史
                const currentData = {
                    time: now,
                    values: currentValues
                };

                allParameterData.push(currentData);

                // 保持数据点数量在合理范围内
                if (allParameterData.length > 100) {
                    allParameterData.shift();
                }

                // 更新当前时间索引
                currentTimeIndex = allParameterData.length - 1;

                // 更新图表显示
                updateChartDisplay();

                // 更新导航按钮状态
                updateNavigationButtons();
            }, 1000);
        }

        /**
         * 获取历史数据模式
         * @param {string} paramId - 参数ID
         * @param {string} historyTime - 历史时间点
         * @returns {Array} 历史数据模式数组
         */
        function getHistoryPattern(paramId, historyTime) {
            // 根据不同的历史时间点和参数生成不同的固定模式
            const patterns = {
                'manual-reactive': [1, 1, 0, 1, 0, 0, 1, 1, 0, 1],
                'pll-mode': [0, 1, 1, 1, 0, 1, 0, 0, 1, 1],
                'control-mode': [1, 0, 1, 0, 1, 1, 0, 1, 0, 0],
                'low-voltage-support': [0, 0, 1, 1, 1, 0, 1, 0, 1, 0],
                'unit-level': [1, 1, 1, 0, 0, 1, 0, 1, 1, 0],
                'ct-sampling': [0, 1, 0, 1, 1, 0, 0, 1, 0, 1],
                'phase-number': [1, 0, 0, 1, 1, 1, 0, 0, 1, 1],
                'master-slave': [0, 0, 1, 0, 1, 1, 1, 0, 0, 1],
                'svg-connection': [1, 1, 0, 0, 0, 1, 1, 1, 0, 0],
                'balance-control': [0, 1, 1, 0, 1, 0, 1, 1, 0, 1],
                'voltage-feedforward': [1, 0, 1, 1, 0, 0, 1, 0, 1, 0],
                'idle-mode': [0, 0, 0, 1, 1, 1, 0, 1, 1, 1]
            };

            return patterns[paramId] || [0, 1, 0, 1, 0, 1, 0, 1, 0, 1];
        }



        /**
         * 重置图表数据
         */
        function resetChart() {
            // 清空所有数据
            allParameterData = [];
            currentTimeIndex = 0;

            // 重置所有参数状态为0
            parameters.forEach(param => {
                parameterStates[param.id] = 0;
            });

            // 重置图表
            if (parameterChart) {
                const option = parameterChart.getOption();
                option.series = [];
                parameterChart.setOption(option);
            }

            // 重置曲线类型选择
            const curveTypeSelect = document.getElementById('curveTypeSelect');
            if (curveTypeSelect) {
                curveTypeSelect.value = '';
            }

            // 重置所有参数开关状态
            document.querySelectorAll('.parameter-switch.active').forEach(el => {
                el.classList.remove('active');
            });

            // 重置自动滚动状态
            isAutoScrollEnabled = true;
            const pauseBtn = document.getElementById('pauseAutoScrollBtn');
            const enableBtn = document.getElementById('enableAutoScrollBtn');
            if (pauseBtn && enableBtn) {
                pauseBtn.style.display = 'flex';
                enableBtn.style.display = 'none';
                pauseBtn.classList.remove('active');
                enableBtn.classList.add('active');
            }

            // 隐藏参数控制
            hideParameterControls();

            // 更新导航按钮状态
            updateNavigationButtons();

            console.log('图表已重置');
        }

        /**
         * 导出图表数据
         */
        function exportChart() {
            if (!parameterChart) return;
            
            // 导出为图片
            const url = parameterChart.getDataURL({
                type: 'png',
                backgroundColor: '#1a1f2e'
            });
            
            const link = document.createElement('a');
            link.download = `参数曲线_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
            link.href = url;
            link.click();
        }
    </script>
</body>
</html>
